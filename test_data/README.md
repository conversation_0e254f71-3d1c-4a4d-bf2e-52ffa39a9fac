# Test Data for PyQBF

This directory contains example QDIMACS files for testing the QBF parser and solver.

## Files

### Satisfiable Formulas

- **`satisfiable_tautology.qdimacs`** - Simple tautology: `∃x.(x ∨ ¬x)`
  - Always satisfiable regardless of x's value
  - Tests basic existential quantifier with tautological clauses

- **`satisfiable_mixed_quantifiers.qdimacs`** - Mixed quantifiers: `∃x∀y.(x ∨ y)`
  - Satisfiable by setting x=true (then x∨y is true for any y)
  - Tests exists followed by forall quantifier

- **`classic_qbf_example.qdimacs`** - Classic example: `∀x.∃y.(x ∨ y) ∧ (¬x ∨ y)`
  - Satisfiable by setting y=true for any x
  - Common example from QBF literature

### Unsatisfiable Formulas

- **`unsatisfiable_example.qdimacs`** - Contradiction: `∀x∃y.(x ∨ y) ∧ (¬x ∨ ¬y) ∧ (x ∨ ¬y) ∧ (¬x ∨ y)`
  - Forces y to be both true and false for any x
  - Tests unsatisfiable QBF detection

### Complex Examples

- **`complex_alternating.qdimacs`** - Alternating quantifiers: `∀x₁∃x₂∀x₃∃x₄.(x₁ ∨ x₂ ∨ x₃) ∧ (¬x₁ ∨ ¬x₃ ∨ x₄)`
  - Tests alternating ∀∃∀∃ quantifier pattern
  - More complex quantifier structure

- **`large_formula.qdimacs`** - Larger formula with 5 variables
  - Tests multiple variables per quantifier block
  - More clauses and variables

## Usage

These files are used by:
- `file_test.py` - Tests file reading and parsing functionality
- Manual testing with `main.py` - e.g., `python main.py test_data/satisfiable_tautology.qdimacs`
- Integration testing of the complete pipeline

## Expected Results

Currently, the placeholder solver returns "UNSAT" for all formulas. When a real QBF solver is implemented:
- Satisfiable formulas should return "SAT"
- Unsatisfiable formulas should return "UNSAT"
